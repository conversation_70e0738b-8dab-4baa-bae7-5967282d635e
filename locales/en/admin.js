export default {
  register: {
    logo: 'Company logo',
    name: 'Name',
    company_name: 'Company Name',
    company_region: 'Company Region',
    email: 'Email',
    phone: 'phone number',
    company_website: 'company website',
    cr_number: 'CR Number',
    password: 'password',
    confirm_password: 'confirm password',
    company_profile: 'company’s Profile',
    name: 'Name',
    user_image: 'Profile Image',
    message: 'Message',
  },
  select: 'Select',
  signup: 'Sign Up',
  enter_your: 'Enter your',
  click_to_upload: 'Click to upload',
  drag_and_drop: 'or drag and drop',
  signup_button: 'Sign Up',
  cancel: 'Cancel',
  extension_error:
    'This file is not compatible with the available file formats!',
  date_must_be_future: 'The selected date must be today or in the future',
  signin_title: 'Log in',
  signin_desc: 'Welcome back! Please enter your details.',
  forget_password: 'Forget password ',
  signup_with_google: 'Sign in with Google',
  havenot_account: 'Don’t have an account?',
  forget_email_desc: "Don't worry, we'll send you reset instructions.",
  reset_password: 'Reset password',
  back_to_login: 'Back to login',
  code_sent: 'O<PERSON> has been sent to your email address successfully',
  sidebar: {
    overview: 'Overview',
    my_bids: 'My Bids',
    invited_bids: 'Invited Bids',
    branch_users: 'Branch Users',
    website: 'Website',
    deletion_requests: 'Bid Deletion Requests',
    deleted_bids: 'Deleted Bids',
  },
  users: 'Users',
  user: 'User',
  upgrade_plan: 'Upgrade Plan',
  logout: 'Logout',
  hello: 'Hello,',
  create_bid: 'Create Bid',
  statics: {
    out_of: 'Out of',
    bid: 'Bid',
    active_bids: 'Active Bids',
    submitted_offers: 'Submitted Offers',
    invited_bids: 'Invited bids',
    added_users: 'Added Branch Users',
    users: 'Users',
    current_plan: 'Current Plan',
  },
  my_active_bids: 'My Active Bids',
  currancy: 'SAR',
  available_offers: 'Available offers',
  view_details: 'View Details',
  my_bids: 'My Bids',
  all: 'All',
  active: 'Active',
  expired: 'Expired',
  days: 'Days',
  hours: 'Hours Left',
  expired: 'Expired',
  closed: 'Closed',
  bid_form: {
    name: 'Bid Name',
    type: 'Bid Type',
    region: 'Bid Region',
    expire_date: 'Bid Expire Date',
    expire_time: 'Bid Expire Time',
    upload_attach: 'Upload Attachments',
    upload_invitation_attach: 'Upload Bid Invitation File',

    show_prices: 'Show prices to other bidders',
    success_added_bid: 'Bid has been added successfully !',
    success_updated_bid: 'Bid has been updated successfully !',
    pricing_from: 'Pricing time',
    pricing_to: 'Pricing time to',
    pricing_time: 'Pricing Time : ',
  },
  enter: 'Enter',
  update_bid: 'Edit Bid',
  mybids_list: 'My Bids List',
  close_bid: 'Close Bid',
  attachments: 'Attachments',
  invitations: 'Invitations',
  invite_seller: 'Invite Seller',
  lowest_price: 'Lowest Price',
  code_error: 'You must enter a valid verification code',
  verification_code: 'Verification Code',
  reset_info_text: 'You must enter you new password and submit to reset ',
  success_reset_pass: 'Password has been reset successfully !',
  available_bids: 'Invited Bids',
  apply_now: 'Apply Now',
  apply_offer: 'Apply for offer',
  edit_offer: 'Edit your offer',
  offer_form: {
    price: 'Price',
  },
  submit: 'Submit',
  offer_added_success: 'Offer has been added successfully !',
  offer_updated_success: 'Offer has been updated successfully !',
  submitted_offers: 'Submitted offers',
  offer: 'Offer',
  your_offer: 'Your offer',
  comments: 'Comments',
  reply_to_comment: 'Reply to this comment',
  add_comment: 'Add new comment',
  invite_more_sellers: 'Invite More Sellers to bid',
  invite_more_sellers_text:
    'Invite sellers by their email to be able to apply for your bid',
  invited_success: 'Invitation has been sent to bidders successfully !',
  latest_activity: 'Latest Activities',
  login_first: 'You must login first to proceed the operation !',
  add_user: 'Add User',
  added_users: 'added users',
  add_branch_user: 'Add Branch Users',
  add_branch_user_text:
    'Add your team members as a branch users to get your work done.',
  branch_user_success: 'Branch users has been added successfully !',
  branch_table: {
    name: 'Name',
    email: 'Email',
    phone: 'Phone number',
    created: 'Created Bids',
    submitted: 'Offers Submitted',
  },
  login_as: 'Login as',
  close_reason: 'Close Reason',
  close_bid_text:
    'Please choose the reason for closing, then click Confirm. The tender will be closed.',
  closed_success: 'Bid has been closed successfully !',
  bidder_profile: 'Bidder Profile',
  personal_info: 'Personal Info',
  location: 'Location',
  subcommment_error: 'You must enter sub comment first !',
  comment_success: 'Comment has been sent successfully !',
  notifications: 'Notifications',
  new_notifications: 'New Notifications',
  mark_as_read: 'Mark all as read',
  branch_user: 'Branch User',
  branch_logout:
    'Branch user account has been logged out and back into the main account!',
  free_trail_plan: 'Choose plan',
  my_profile: 'My Profile',
  updated_branch_user:
    'Branch usar information has been updated successfully !',
  you_can_add: 'You can only add up to ',
  upgrade_for_bussiness: 'upgrade plan if your business if growing.',
  out_of: 'out of ',
  update_branch_user: 'Update branch user information',
  update_branch_user_text:
    'Please enter the required information, then confirm to save it.',
  upgrade_plan: 'Upgrade plan',
  check_package: 'Are you sure you want to subscribe to this package ?',
  edit_profile: 'Edit profile',
  profile_updated: 'Done .. Profile has been updated !',
  free_trail: 'Free Trial',
  free_trail_desc:
    'Please subscribe to one of munaqes plans to get your work done.',
  check_plans: 'Check Munaqes Plans',
  thanks: 'Ok, Thanks',
  payment_success_title: 'Congratulations 🎉',
  payment_success_desc:
    'The payment has been completed successfully and the package has been subscribed.',
  payment_failed_title: 'Payment Failed',
  payment_failed_desc:
    'An error occurred during the payment process, please try again',
  by_admin: 'By Admin',
  pricing_hint:
    "Leave the pricing time fields empty if you don't wont to specify pricing time.",
  manage_pricing:
    'Manage pricing times on the bid (to be between 30 minutes to 10 hours of the last time of the bid)',
  login_success: 'Login has been completed successfully .. Welcome back !',
  no_package: 'Expired Package',
  status: 'Status',
  active: 'Active',
  inactive: 'Inactive',
  delete_user: 'Delete User',
  user_deleted: 'Done .. User has been deleted !',
  complete_info: 'Complete Information.',
  bidder: 'Bidder',
  delete_notify: 'Clear Notifications',
  half_hour: 'Half an hour',
  hours: 'Hours',
  max_upload: 'Max upload size (100 MB)',
  min: 'Minutes',
  size_error: 'You have exceeded the file size limit',
  external_url: 'External files url',
  external_bid_invite_url: 'Bid Invitation File Url',
  view_offers_log: 'View offers log',
  offer_log: 'Bidder Offers log',
  delete_notify_desc: 'Are you sure you want to clear all notifications?',
  offer: 'Offer',
  current_offer: 'Current Offer',
  ratio_hint: ' from the 1st to the last offer',
  decrease: 'decrease',
  increase: 'increase',
  price_validation: "You're not the lowest price yet !",
  accept: 'I agree to',
  terms_required: 'You must agree to terms & condition !',
  no_subscribe: 'No Subscription',
  no_subscribe_desc:
    'You are not subscribed to a package or your package has expired.. Please subscribe to a package and try again',
  sub_alert_text: 'Kindly subscribe to a plan to start using Munaqes',
  pricing_alert: 'Prices not includes VAT',
  subscribed_successfully:
    'Subscription in package has been done successfully !',
  apply: 'Apply',
  coupon_code_desc: 'Coupon Code',
  hours: 'Hr',
  mins: 'Mins',
  secds: 'Sec',
  logout_success: 'You have been logged out successfully !',
  invite_bidders: 'Add bidders email address (minimum two emails)',
  invite_bidders_validation: 'You must add at least 2 emails',
  invite_bidders_validation_min_one: 'You must add at least 1 emails',
  private_attachments: 'Bid Private Attachments',
  private_bid_attachments: 'Bid Attachments Link',
  has_valid_subscription: 'has a valid subscription.',
  type_region_required: 'You must choose the bid type and region first',
  list_available_invitations: 'List of available bidders for invitations',
  custom_invited_emails: 'Add the emails of the bidders you want to invite',
  invite_from_existed_emails: 'Invite from existed emails',
  delete_bid: 'Delete Bid',
  delete_bid_desc: 'Are you sure you want to delete this bid?',
  delete: 'Delete',
  deleted_success: 'Bid has been deleted successfully !',
  search: 'Search Here..',
  generate_report: 'Generate Report',
  generate_report_desc:
    'Please write your comments to complete the report creation for you.',
  notes: 'Notes',
  report_exist: 'Report has been created for previous offer',
  pending_deletion: 'Pending Deletion',
  deletion_request: 'Bid Deletion Requests',
  approve_deletion: 'Approve Deletion',
  reject_deletion: 'Reject Deletion',
  deletion_rejects: 'Deletion request has been rejected',
  other: 'Other',
  view_report: 'View Report',
  actions: {
    accept: 'Accept',
    reject: 'Reject',
  },
  bid_table: {
    name: 'Bid Name',
    region: 'Region',
    type: 'Bid Type',
    offersCount: 'Offers Count',
    actions: '',
  },
  restore: 'Restore',
  forceDelete: 'Delete',
}
