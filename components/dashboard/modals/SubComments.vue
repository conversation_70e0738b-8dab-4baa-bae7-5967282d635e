<template>
  <b-modal
    id="subcomments"
    size="lg"
    hide-header
    hide-footer
    no-close-on-backdrop
  >
    <div class="modal_wrapper" v-if="item != null">
      <div class="main_comment_wrapper">
        <h5 class="title">
          {{ getCommentDisplayName(item) }}
        </h5>
        <p class="message">
          {{ item.comment }}
        </p>
      </div>
      <!-- end::main_comment_wrapper -->
      <div class="subcomments_list">
        <div class="wrapper" v-for="(comment, idx) in item.replys" :key="idx">
          <h5 class="title">
            {{ getCommentDisplayName(comment) }}
          </h5>
          <p class="message">
            {{ comment.comment }}
          </p>
        </div>
        <!-- end::wrapper -->
      </div>
      <!-- end::subcomments_list -->

      <div class="form_actions">
        <button
          type="button"
          class="btn btn-default"
          @click="$emit('hide-modal', 'subcomments')"
        >
          {{ $t('admin.cancel') }}
        </button>
      </div>
    </div>
  </b-modal>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'SubComment',
  props: ['item', 'bid'],
  mixins: [require('~/mixins/mixins.js').default],
  computed: {
    ...mapState({
      branch_user: (state) => state.localStorage.branch_user,
    }),
    // Computed property to determine comment display name based on visibility rules
    getCommentDisplayName() {
      return (comment) => {
        // Check if current user is admin
        const isCurrentUserAdmin = this.userData?.is_admin === true

        // Check if current user is a branch user
        const isCurrentUserBranchUser = this.branch_user !== null

        // Check if comment was made by current user
        const isCommentByCurrentUser = comment.client_id === this.userData?.id

        // Check if comment was made by bid owner (admin or their branch users)
        const isCommentByBidOwner = comment.byBidOwner === true

        // Check if comment was made by a branch user
        const isCommentByBranchUser = comment.is_branch_user === true

        // Check if comment was made by an admin
        const isCommentByAdmin = comment.is_admin === true

        // Rule 1: For comments made by branch users - display admin's name
        if (isCommentByBranchUser) {
          return comment.admin_name || comment.name
        }

        // Rule 2: For admin users
        if (isCurrentUserAdmin) {
          // Admin sees their own name on their comments
          if (isCommentByCurrentUser) {
            return comment.name
          }
          // Admin sees their branch users' comments labeled with admin's name
          if (isCommentByBranchUser) {
            return comment.admin_name || comment.name
          }
          // Admin sees other users' names when bid status is 1 (active)
          if (this.bid.status === 1) {
            return comment.name
          }
          // For closed bids (status 2), admin sees all names
          if (this.bid.status === 2) {
            return comment.name
          }
          // Default for admin
          return comment.name
        }

        // Rule 3: For branch users - they see admin's name on their own comments
        if (isCurrentUserBranchUser) {
          if (isCommentByCurrentUser) {
            return comment.admin_name || comment.name
          }
          // Branch user sees admin names on admin comments
          if (isCommentByAdmin || isCommentByBidOwner) {
            return comment.name
          }
          // Branch user sees admin names on other branch user comments
          if (isCommentByBranchUser) {
            return comment.admin_name || comment.name
          }
          // Branch user should NOT see other regular users' names
          return this.$t('admin.bidder')
        }

        // Rule 4: For regular users (non-admin)
        if (!isCurrentUserAdmin && !isCurrentUserBranchUser) {
          // User sees their own name on their comments
          if (isCommentByCurrentUser) {
            return comment.name
          }
          // User sees admin names on admin comments
          if (isCommentByAdmin || isCommentByBidOwner) {
            return comment.name
          }
          // User sees admin names on branch user comments
          if (isCommentByBranchUser) {
            return comment.admin_name || comment.name
          }
          // User should NOT see other regular users' names - show as "Bidder"
          return this.$t('admin.bidder')
        }

        // Default fallback
        return this.$t('admin.bidder')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.modal_wrapper {
  position: relative;
  .form_actions {
    display: flex;
    justify-content: flex-end;
    .btn {
      border-color: #dc3545;
      color: #dc3545;
    }
  }
}
.main_comment_wrapper {
  padding: 25px;
  border-radius: 8px;
  background-color: #f9fafb;
  margin-bottom: 20px;
  .title {
    font-size: 26px;
    font-weight: 600;
    margin-bottom: 0;
  }
  p {
    margin-bottom: 0;
  }
}
.subcomments_list {
  //   padding-inline: 30px;
  .wrapper {
    border-bottom: 1px solid #eee;
    padding: 15px;
    &:last-child {
      border-bottom: none;
    }
    .title {
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 0;
    }
    p {
      margin-bottom: 0;
    }
  }
}
</style>
