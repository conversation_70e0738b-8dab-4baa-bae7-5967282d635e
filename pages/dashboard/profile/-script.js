// importing vuex tools
import { mapGetters } from 'vuex'

export default {
  name: 'RegisterForm',
  layout: 'ExternalForm',
  data() {
    return {
      form: {
        company_name: null,
        email: null,
        phone: null,
        website: null,
        cr_number: null,
        password: null,
        password_confirmation: null,
        logo: null,
        cv: null,
        region_id: null,
      },
      region_id: null,
      logo: null,
      cv: null,
      disabled: false,
    }
  },
  async mounted() {
    // set user profile data
    this.form.company_name = this.userData.company_name
    this.form.email = this.userData.email
    this.form.phone = this.userData.phone
    this.form.cr_number = this.userData.cr_number
    this.form.website = this.userData.website
    this.region_id = this.userData.region
    this.form.region_id = this.userData.region?.id
    this.logo = {
      name: this.userData.logo.split('/').pop(),
      media: this.userData.logo,
    }
    this.cv = {
      name: this.userData.cv.split('/').pop(),
      media: this.userData.cv,
    }
    // get regions dropdown items
    await this.$axios.$get('/utils/regions').then((res) => {
      this.$store.commit('localStorage/SET_REGIONS', res.data)
    })
  },
  watch: {
    region_id(current) {
      if (current != null) {
        this.form.region_id = current.id
      }
    },
  },
  computed: {
    ...mapGetters({
      regions: ['localStorage/get_regions'],
    }),
  },
  methods: {
    uploadFiles($event, type) {
      const file = $event.target.files[0]
      const imageExt = ['png', 'jpg', 'jpeg', 'pdf', 'docx']
      const extension = $event.target.files[0].name
        .split('.')
        .pop()
        .toLowerCase()

      if (imageExt.includes(extension)) {
        if (type == 'logo') {
          this.form.logo = file
          this.logo = {
            name: file.name,
            media: URL.createObjectURL(file),
          }
        } else if (type == 'cv') {
          this.form.cv = file
          this.cv = {
            name: file.name,
            media: URL.createObjectURL(file),
          }
        }
      } else {
        this.TriggerNotify('error', this.$t('admin.extension_error'))
      }
    },
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleReq()
        }
      })
    },
    async handleReq() {
      this.disabled = true
      const form_data = new FormData()

      for (let key in this.form) {
        if (this.form[key] != null) {
          if (key == 'region_id') {
            form_data.append('region_id', this.region_id.id)
          } else {
            form_data.append(key, this.form[key])
          }
        }
      }

      await this.$axios
        .post('/client/auth/editProfile', form_data)
        .then((res) => {
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            this.$store.commit('localStorage/SET_USER_DATA', res.data.data)
            this.TriggerNotify('success', this.$t('admin.profile_updated'))
            this.$router.replace(this.localePath({ name: 'dashboard' }))
          } else {
            this.TriggerNotify(this.notify.type, this.notify.message)
            this.disabled = false
          }
        })
        .catch((err) => {
          this.TriggerNotify('error', err.response.data.message)
          this.disabled = false
        })

      this.disabled = false
    },
  },
}
