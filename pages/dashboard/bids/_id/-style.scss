.bid_details_wrapper {
  .white_wrapper {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px 22px;
    @media (max-width: 550px) {
      padding: 15px 12px;
    }
    &.with_border {
      border: 1px solid #eee;
    }
  }
  .main_info {
    margin-bottom: 32px;
    .back_button {
      margin-bottom: 20px;
      a {
        font-size: 18px;
        font-weight: 500;
        color: #344054;

        .icon {
          width: 14px;
          height: 14px;
          transform: rotate(90deg);
          margin-inline-end: 8px;
        }
      }
    }
    .line {
      display: flex;
      align-items: center;
      margin-bottom: 22px;
      flex-wrap: wrap;
      &.space_between {
        justify-content: space-between;
        @media (max-width: 1275px) {
          flex-wrap: wrap;
        }
      }
      &.flex-clear {
        display: block;
      }
      .wrapper {
        display: flex;
        align-items: center;
        margin-inline-end: 15px;
        flex-wrap: wrap;
        .label {
          margin-inline-end: 8px;
          font-weight: 700;
        }
        &.attachs {
          padding-inline-start: 50px;
          flex-wrap: wrap;
          margin-top: 10px;
          width: 70%;

          a {
            border-radius: 25px;
            padding: 4px 25px;
            background-color: #f2f4f7;
            color: $black-text-color;
            font-weight: 600;
            font-size: 14px;
            margin-inline-end: 8px;
            margin-bottom: 8px;
            max-width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        &.bidders_wrapper {
          padding-inline-start: 50px;
          flex-wrap: wrap;
          margin-top: 10px;
          img {
            width: 45px;
            height: 45px;
            border-radius: 100%;
            outline: 4px solid #fff;
            border: 2px solid #f00;
            &.registered {
              border-color: $base-color;
            }
          }
          .btn {
            width: 45px;
            height: 45px;
            border-radius: 100%;
            border: 2px dashed rgba(208, 213, 221, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-inline-start: 5px;
            box-shadow: none;
            .icon {
              width: 20px;
              height: 20px;
              stroke: rgba(152, 162, 179, 1);
            }
          }
        }
        &.options {
          display: flex;
          align-items: center;
          @media (max-width: 1275px) {
            margin-top: 15px;
          }
          .btn {
            border-radius: 8px;
            @media (max-width: 500px) {
              min-width: auto;
              padding-inline: 10px;
              padding-block: 8px;
            }
            .icon {
              width: 18px;
              height: 18px;
              margin-inline-end: 6px;
            }
            &:first-child {
              background-color: #fff;
              border: 1px solid #d0d5dd;
              color: #000;
              margin-inline-end: 10px;
            }
            &:last-child,
            &.danger-btn {
              background-color: #fff5f5;
              color: rgba(204, 0, 0, 1);
            }
            &.danger-btn {
              margin-inline-end: 10px;
            }
          }
        }
        &.options_user {
          display: flex;
          align-items: center;
          .btn {
            border-radius: 8px;
            &.btn-filled {
              background-color: $base-color;
              color: #fff;
            }
            &.btn-transparent {
              background-color: $base-color-opacity;
              color: $base-color;
            }
          }
        }
        .name {
          font-size: 28px;
          color: #000;
          font-weight: 700;
          margin-inline-end: 15px;
          margin-bottom: 0;
        }
        .serial {
          padding: 3px 15px;
          background-color: #f9fafb;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 600;
          margin-inline-end: 10px;
          color: rgba(29, 41, 57, 1);
        }
        .count {
          width: 30px;
          height: 30px;
          border-radius: 100%;
          background-color: #ebfaf5;
          border-radius: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          font-weight: 600;
          color: $base-color;
          margin-inline-start: 8px;
        }
        .type {
          padding: 3px 15px;
          background-color: rgba(235, 250, 245, 1);
          border-radius: 4px;
          color: $base-color;
          font-size: 14px;
          font-weight: 500;
        }
        .icon_wrapper {
          width: 40px;
          height: 40px;
          border-radius: 100%;
          border: 1px solid #eaecf0;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-inline-end: 10px;
          .icon {
            width: 22px;
            height: 22px;
          }
        }
        .value {
          font-weight: 600;
          color: $black-text-color;
          display: flex;
          align-items: center;
        }
        .time_last {
          padding: 3px 15px;
          border-radius: 4px;
          background-color: #fff;
          border: 1px solid rgba(204, 0, 0, 1);
          margin-inline-start: 10px;
          display: inline-block;
          color: rgba(204, 0, 0, 1);
          font-weight: 500;
          display: flex;
          align-items: center;
          margin-top: 5px;
          &.active {
            background-color: rgba(235, 250, 245, 1) !important;
            border: 1px solid $base-color !important;
            color: $base-color !important;
          }
        }
      }
    }
  }
  .offers_wrapper {
    .title_box {
      margin-bottom: 20px;
      .title {
        font-size: 26px;
        font-weight: 600;
        margin-bottom: 0;
      }
    }
    .offers_list {
      min-height: 400px;
      max-height: 500px;
      padding-inline-end: 20px;
      border-inline-end: 1px solid #ddd;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 6px;
      }
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 25px;
      }
      &::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 25px;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
      .line {
        display: flex;
        background-color: #fff;
        border-radius: 8px;
        flex-wrap: wrap;
        border: 1px solid #eee;
        padding: 16px 24px;
        // align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all 0.25s;
        &:hover {
          border-color: $base-color;
        }
        &.flex_clear {
          align-items: flex-start;
        }
        &.owned {
          border-color: $base-color;
        }

        &:last-child {
          margin-bottom: 0;
        }
        .item {
          &.options {
            display: flex;
            justify-content: flex-end;
            width: 100%;
            a {
              font-weight: 500;
              color: $base-color;
              .icon {
                width: 13px;
                height: 13px;
                stroke: $base-color;
                transform: rotate(-180deg);
                margin-inline-start: 5px;
              }
            }
          }
          &.price_wrapper {
            width: 35%;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
          }
          &.main_info_data {
            width: 65%;
          }
          .title {
            font-size: 18px;
            font-weight: 600;
            .hoverable {
              cursor: pointer;
              transition: all 0.25s;
              &:hover {
                color: $base-color;
              }
            }
          }
          .icon {
            width: 23px;
            height: 23px;
            margin-inline-start: 8px;
            transform: rotate(180deg);
            &.arrow_up {
              transform: rotate(-180deg);
            }
          }
          .price {
            font-size: 20px;
            font-weight: 600;
            white-space: nowrap;
          }
          .btn-transparent {
            background-color: $base-color-opacity;
            color: $base-color;
            margin-top: 5px;
          }
        }
        .offer_label {
          padding: 4px 10px;
          background-color: #f2f4f7;
          border-radius: 4px;
          color: rgba(52, 64, 84, 1);
          font-size: 14px;
          font-weight: 600;
          margin-inline-start: 10px;
        }
      }
      .info_wrapper {
        margin-top: 22px;
        .info {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
          flex-wrap: wrap;
          .icon_wrapper {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            border: 1px solid rgba(208, 213, 221, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            .icon {
              width: 18px;
              height: 18px;
              margin-inline-start: 0;
            }
          }
          span {
            width: calc(100% - 50px);
            margin-inline-start: 10px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            color: rgba(71, 84, 103, 1);
            font-weight: 600;
            display: flex;
            align-items: center;
            .count {
              width: 30px;
              height: 30px;
              border-radius: 100%;
              background-color: #ebfaf5;
              border-radius: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 14px;
              font-weight: 600;
              color: #1e805d;
              margin-inline-start: 8px;
            }
          }

          .attachs {
            width: 100%;
            padding-inline-start: 50px;
            margin-top: 8px;
            display: flex;
            align-items: center;
            a {
              border-radius: 25px;
              padding: 4px 25px;
              background-color: #f2f4f7;
              color: #1d2939;
              font-weight: 600;
              font-size: 14px;
              margin-inline-end: 8px;
              margin-bottom: 8px;
              max-width: 250px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
  .comments_wrapper {
    .title_box {
      margin-bottom: 20px;
      .title {
        font-size: 26px;
        font-weight: 600;
        margin-bottom: 0;
      }
    }
    .main_comment_form {
      .form-control {
        border-radius: 25px !important;
        background-color: $base-color-opacity;
        padding-inline-start: 25px;
        border: none;
        &::placeholder {
          font-weight: 600;
          color: $base-color;
        }
      }
    }
    .comments_list {
      max-height: 500px;
      overflow-y: auto;
      padding-inline-end: 8px;
      &::-webkit-scrollbar {
        width: 6px;
      }
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 25px;
      }
      &::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 25px;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
      .line {
        border: 1px solid #eaecf0;
        border-radius: 8px;
        background-color: #fff;
        padding: 15px 20px;
        margin-bottom: 15px;
        &:last-child {
          margin-bottom: 0;
        }
        .message {
          font-size: 14px;
          color: #344054;
          line-height: 20px;
          margin-bottom: 8px;
        }
        .show_subcomments {
          color: #667085;
          font-size: 14px;
          font-weight: 600;
          display: inline-block;
          margin-bottom: 15px;
        }
        .form-group {
          margin-bottom: 0;
          .form-control {
            font-size: 14px !important;
            border-radius: 25px !important;
            background-color: #f9fafb;
            color: #667085;
            padding-inline-start: 25px;
            resize: none;
            &::placeholder {
              font-weight: 600;
            }
          }
        }
      }
    }
  }
  .b-sidebar {
    &.bg-light {
      background-color: #fff !important;
    }

    .basic_info {
      padding-bottom: 20px;
      margin-bottom: 20px;
      border-bottom: 1px solid #eee;
      .avatar_wrapper {
        padding: 10px 15px;
        border-radius: 8px;
        background-color: #f9fafb;
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        img {
          width: 75px;
          height: 75px;
          border: 1px solid #eee;
          border-radius: 100%;
          margin-inline-end: 15px;
        }
        h4 {
          font-size: 20px;
          font-weight: 600;
          color: #000;
        }
      }
      .personal_info {
        .title {
          margin-bottom: 15px;
          font-size: 18px;
          font-weight: 600;
          color: $black-text-color;
        }
        .wrapper {
          margin-bottom: 20px;
          p {
            font-weight: 500;
            &:first-child {
              margin-bottom: 5px;
              color: rgba(102, 112, 133, 1);
              font-size: 15px;
            }
            &:last-child {
              margin-bottom: 0;
              font-size: 16px;
              color: $black-text-color;
            }
          }
        }
      }
    }
    .company_info {
      .title {
        margin-bottom: 15px;
        font-size: 18px;
        font-weight: 600;
        color: $black-text-color;
      }
      .wrapper {
        margin-bottom: 20px;
        p {
          font-weight: 500;
          &:first-child {
            margin-bottom: 5px;
            color: rgba(102, 112, 133, 1);
            font-size: 15px;
          }
          &:last-child {
            margin-bottom: 0;
            font-size: 16px;
            color: $black-text-color;
          }
        }
        a {
          font-weight: 500;
          color: $black-text-color;
          display: block;
          width: 100%;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }
}

[dir='rtl'] {
  .bid_details_wrapper .main_info .back_button a .icon {
    transform: rotate(270deg);
  }
}

.send_wrappeer {
  position: relative;
  border-radius: 25px;
  overflow: hidden;
  .btn-default {
    height: 45px;
    width: 70px;
    min-width: auto;
    background-color: $base-color;
    .icon {
      width: 26px;
      height: 26px;
      fill: #fff;
    }
  }
}

[dir='rtl'] {
  .bid_details_wrapper
    .offers_wrapper
    .offers_list
    .line
    .item.options
    a
    .icon {
    transform: rotate(0);
  }
}

#offers_log {
  width: 450px;
  @media (max-width: 991px) {
    width: 100%;
  }
  .offer_card_wrapper {
    padding: 15px;
    border-radius: 5px;
    background-color: rgba(249, 250, 251, 1);
    margin-bottom: 20px;
    border: 1px solid transparent;
    &.current {
      border-color: $base-color;
    }
    .line {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
      .title {
        color: rgba(71, 84, 103, 1);
        font-weight: 600;
      }
      .date {
        padding: 5px 15px;
        background-color: rgba(242, 244, 247, 1);
        border-radius: 4px;
        font-size: 13px;
        font-weight: 500;
      }
      .price {
        font-size: 18px;
        font-weight: 600;
        color: $base-color;
      }
      .ratio {
        padding: 5px 10px;
        border: 1px solid rgba(89, 216, 171, 1);
        background-color: rgba(249, 250, 251, 1);
        border-radius: 5px;
        .percent {
          font-size: 14px;
          font-weight: 500;
          color: rgba(52, 64, 84, 1);
          border-radius: 5px;
          background-color: #fff;
          border-radius: 5px;
          padding: 4px 15px;
          .icon {
            margin-inline-start: 4px;
            width: 15px;
            height: 15px;
            stroke: rgba(204, 0, 0, 1);
            &.plus {
              stroke: #1e805d;
            }
          }
        }
        .text {
          font-size: 15px;
          font-weight: 500;
          color: rgba(52, 64, 84, 1);
        }
      }
    }
  }
}
.subscription-mark {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  border-radius: 100%;
  background: #fff;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4caf50;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
  cursor: pointer;
}
