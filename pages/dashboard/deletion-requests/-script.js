// import vuex tools
import { mapState } from 'vuex'
// importing components
import SkeletonLoading from '~/components/dashboard/reuseable/SkeletonLoading.vue'
import BidCard from '~/components/dashboard/reuseable/BidCard.vue'

export default {
  name: 'DeletionRequests',
  layout: 'dashboard',
  components: { SkeletonLoading, BidCard },
  async asyncData(context) {
    await context.$axios.$get('/v2/bids/pending-deletion').then((res) => {
      context.store.commit('bids/SET_DELETION_REQUESTS', res.data)
    })
  },
  data() {
    return {
      loading: false,
    }
  },
  computed: {
    ...mapState({
      bids: (state) => state.bids.deletion_requests,
    }),
  },
}
