// importing vuex tools
import { mapState } from 'vuex'
// importing components
import SkeletonLoading from '~/components/dashboard/reuseable/SkeletonLoading.vue'
import AllBids from '~/components/dashboard/pages-components/AllBids.vue'
import ActiveBids from '~/components/dashboard/pages-components/ActiveBids.vue'
import ExpiredBids from '~/components/dashboard/pages-components/ExpiredBids.vue'

export default {
  name: 'MyBids',
  layout: 'dashboard',
  components: { SkeletonLoading, ActiveBids, ExpiredBids, AllBids },
  async asyncData(context) {
    await context.$axios.$get('/bids/myBids').then((res) => {
      context.store.commit('bids/SET_MY_BIDS', res.data)
    })
  },
  data() {
    return {
      loading: false,
      activeComp: 'AllBids',
    }
  },
  computed: {
    ...mapState({
      all_mybids: (state) => state.bids.all_mybids,
      all_active_bids: (state) => state.bids.my_active_bids,
      all_expired_mybids: (state) => state.bids.my_expired_bids,
    }),
  },
  methods: {
    changeStatus(type) {
      const buttons = document.querySelectorAll('.page_header .nav-link')
      buttons.forEach((elem) => {
        elem.classList.remove('active')
      })
      if (type == 'all') {
        this.activeComp = 'AllBids'
        buttons[0].classList.add('active')
      } else if (type == 'active') {
        this.activeComp = 'ActiveBids'
        buttons[1].classList.add('active')
      } else {
        this.activeComp = 'ExpiredBids'
        buttons[2].classList.add('active')
      }
      this.refetchBids(type)
    },
    async refetchBids(type) {
      if (type == 'all') {
        if (this.all_mybids.length == 0) {
          this.loading = true
          await this.$axios.$get(`/bids/myBids`).then((res) => {
            this.$store.commit('bids/SET_MY_BIDS', res.data)
          })
          this.loading = false
        }
      } else if (type == 'active') {
        if (this.all_active_bids.length == 0) {
          this.loading = true
          await this.$axios.$get(`/bids/myBids?status=open`).then((res) => {
            this.$store.commit('bids/SET_MY_ACTIVE_BIDS', res.data)
          })
          this.loading = false
        }
      } else if (type == 'expired') {
        if (this.all_expired_mybids.length == 0) {
          this.loading = true
          await this.$axios.$get(`/bids/myBids?status=closed`).then((res) => {
            this.$store.commit('bids/SET_MY_EXPIRED_BIDS', res.data)
          })
          this.loading = false
        }
      }
    },
  },
}
