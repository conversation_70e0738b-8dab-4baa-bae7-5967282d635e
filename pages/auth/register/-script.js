// importing vuex tools
import { mapGetters } from 'vuex'

export default {
  name: 'RegisterForm',
  layout: 'ExternalForm',
  data() {
    return {
      form: {
        company_name: null,
        email: null,
        phone: null,
        website: null,
        cr_number: null,
        password: null,
        password_confirmation: null,
        logo: null,
        cv: null,
      },
      terms: false,
      region_id: null,
      logo: null,
      cv: null,
      disabled: false,
    }
  },
  async mounted() {
    // get regions dropdown items
    await this.$axios.$get('/utils/regions').then((res) => {
      this.$store.commit('localStorage/SET_REGIONS', res.data)
    })
  },
  computed: {
    ...mapGetters({
      regions: ['localStorage/get_regions'],
    }),
  },
  methods: {
    uploadFiles($event, type) {
      const file = $event.target.files[0]
      const imageExt = ['png', 'jpg', 'jpeg', 'pdf', 'docx']
      const extension = $event.target.files[0].name
        .split('.')
        .pop()
        .toLowerCase()

      if (imageExt.includes(extension)) {
        if (type == 'logo') {
          this.form.logo = file
          this.logo = {
            name: file.name,
            media: URL.createObjectURL(file),
          }
        } else if (type == 'cv') {
          this.form.cv = file
          this.cv = {
            name: file.name,
            media: URL.createObjectURL(file),
          }
        }
      } else {
        this.TriggerNotify('error', this.$t('admin.extension_error'))
      }
    },
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleReq()
        }
      })
    },
    async handleReq() {
      if (this.terms == false) {
        this.TriggerNotify('error', this.$t('admin.terms_required'))
      } else {
        this.disabled = true
        const form_data = new FormData()
        Object.entries(this.form).forEach((entry) => {
          if (entry[1] != null) {
            form_data.append(entry[0], entry[1])
          }
        })
        form_data.append('region_id', this.region_id.value)

        await this.$axios
          .post('/client/auth/register', form_data)
          .then((res) => {
            this.$store.dispatch('localStorage/response_handler', res.data)
            if (this.notify.state == 0) {
              this.$router.push(
                this.localePath({
                  name: 'auth-verify',
                  query: { email: this.form.email },
                })
              )
              this.TriggerNotify(
                this.notify.type,
                this.$t('front.otp_sent_success')
              )
            } else {
              this.TriggerNotify(this.notify.type, this.notify.message)
            }
          })

        this.disabled = false
      }
    },
  },
}
