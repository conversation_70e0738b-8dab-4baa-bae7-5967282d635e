<template>
  <section class="register_main_wrapper">
    <div class="title_box">
      <h2>{{ $t('admin.complete_info') }}</h2>
    </div>
    <!-- end::title_box -->

    <client-only>
      <ValidationObserver ref="form">
        <b-form @submit.prevent="handleForm">
          <div class="row">
            <div class="col-xl-12">
              <b-form-group>
                <b-input-group class="align-items-center">
                  <label class="control-label m-inline-end-20">
                    {{ $t('admin.register.logo') }} <span class="star">*</span>
                  </label>
                  <div class="sm-file-picker">
                    <input
                      type="file"
                      id="profile"
                      @change="uploadFiles($event)"
                    />
                    <label for="profile" class="mb-0">
                      <svg class="icon">
                        <use xlink:href="~/static/sprite.svg#img-pick"></use>
                      </svg>
                    </label>
                  </div>
                  <div class="preview_files sm" v-if="image != null">
                    <a :href="image.media">
                      <img :src="image.media" alt="logo" />
                    </a>
                  </div>
                  <!-- end::preview_files -->
                </b-input-group>
              </b-form-group>
            </div>
            <!-- end::col -->
            <div class="col-lg-6">
              <ValidationProvider rules="required|min:3" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.name') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <b-form-input
                      type="text"
                      v-model="form.name"
                      :class="{ invalid: errors[0] }"
                      :placeholder="$t('admin.register.name')"
                    ></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-6">
              <ValidationProvider rules="required|email" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.email') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group class="has-icon">
                    <template #prepend>
                      <svg class="icon" :class="{ invalid: errors[0] }">
                        <use
                          xlink:href="~/static/icons/regular.svg#envelope"
                        ></use>
                      </svg>
                    </template>
                    <b-form-input
                      type="text"
                      v-model="form.email"
                      :class="{ invalid: errors[0] }"
                      :placeholder="
                        $t('admin.enter_your') +
                        ' ' +
                        $t('admin.register.email')
                      "
                    ></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-6">
              <ValidationProvider
                rules="required|min:9|max:15"
                v-slot="{ errors }"
              >
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.phone') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group class="has-icon">
                    <template #prepend>
                      <svg class="icon" :class="{ invalid: errors[0] }">
                        <use
                          xlink:href="~/static/icons/regular.svg#phone"
                        ></use>
                      </svg>
                    </template>
                    <b-form-input
                      type="text"
                      v-model="form.phone"
                      :class="{ invalid: errors[0] }"
                      :placeholder="
                        $t('admin.enter_your') +
                        ' ' +
                        $t('admin.register.phone')
                      "
                    ></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->
            <div class="col-lg-6">
              <ValidationProvider
                rules="required|min:4|max:4"
                :name="$t('admin.verification_code')"
                v-slot="{ errors }"
              >
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.verification_code') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <b-form-input
                      type="text"
                      v-model="form.code"
                      :class="{ invalid: errors[0] }"
                      :placeholder="
                        $t('admin.enter_your') +
                        ' ' +
                        $t('admin.verification_code')
                      "
                    ></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-6">
              <ValidationProvider
                vid="confirm_password"
                :rules="{
                  required: true,
                  min: 8,
                  regex:
                    '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})',
                }"
                v-slot="{ errors }"
              >
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.password') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group class="has-icon">
                    <template #prepend>
                      <svg class="icon" :class="{ invalid: errors[0] }">
                        <use xlink:href="~/static/icons/regular.svg#lock"></use>
                      </svg>
                    </template>
                    <b-form-input
                      type="password"
                      v-model="form.password"
                      :class="{ invalid: errors[0] }"
                      :placeholder="
                        $t('admin.enter_your') +
                        ' ' +
                        $t('admin.register.password')
                      "
                    ></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-6">
              <ValidationProvider
                rules="required|confirmed:confirm_password"
                v-slot="{ errors }"
              >
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.confirm_password') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group class="has-icon">
                    <template #prepend>
                      <svg class="icon" :class="{ invalid: errors[0] }">
                        <use xlink:href="~/static/icons/regular.svg#lock"></use>
                      </svg>
                    </template>
                    <b-form-input
                      type="password"
                      v-model="password_confirmation"
                      :class="{ invalid: errors[0] }"
                      :placeholder="
                        $t('admin.enter_your') +
                        ' ' +
                        $t('admin.register.confirm_password')
                      "
                    ></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->
          </div>
          <!-- end::row -->
          <div class="card_footer">
            <div class="container-fluid">
              <div class="buttons_wrapper">
                <button type="button" class="btn btn-default">
                  {{ $t('admin.cancel') }}
                </button>
                <button
                  type="submit"
                  class="btn btn-default"
                  :disabled="disabled"
                >
                  <b-spinner variant="light" small v-if="disabled"></b-spinner>
                  <span>{{ $t('admin.submit') }}</span>
                </button>
              </div>
            </div>
          </div>
          <!-- end::footer_wrapper -->
        </b-form>
      </ValidationObserver>
    </client-only>
    <!-- end:: form_wrapper -->
  </section>
</template>

<script src="~/pages/auth/register/branch-user/-script.js"></script>

<style lang="scss">
@import '~/pages/auth/register/-style.scss';
</style>
