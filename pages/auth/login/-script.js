export default {
  name: '<PERSON>ginP<PERSON>',
  layout: 'ExternalForm',
  data() {
    return {
      form: {
        email: null,
        password: null,
        fcm_token: null,
      },
      googleInfo: {
        name: null,
        email: null,
        provider: 'google',
        provider_id: null,
      },
      disabled: false,
    }
  },
  async mounted() {
    let currentToken = null
    Notification.requestPermission().then((permission) => {
      if (permission === 'granted') {
        currentToken = this.$fire.messaging.getToken().then((token) => {
          this.form.fcm_token = token
        })
      } else {
        console.warn('Sorry .. Your browser not support notification !')
      }
    })

    if (currentToken != null) {
      this.form.fcm_token = currentToken
    }

    if (this.$route.query.email) {
      this.form.email = this.$route.query.email
    }

    // this.$store.commit('localStorage/SET_FCM_TOKEN', currentToken)
  },
  methods: {
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleReq()
        }
      })
    },
    async handleReq() {
      this.disabled = true
      await this.$axios
        .post('/client/auth/login', this.form)
        .then(async (res) => {
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            const accessToken = `Bearer ${res.data.data.token}`
            const options = {
              path: '/',
              maxAge: 60 * 60 * 24,
            }
            this.$store.commit('localStorage/SET_USER_DATA', res.data.data.user)
            // this.$store.commit('localStorage/SET_ADMIN_DATA',res.data.data)
            this.$store.commit('localStorage/SET_USER_TOKEN', accessToken)
            this.$cookies.setAll([
              { name: 'userToken', value: accessToken, opts: options },
              { name: 'userData', value: res.data.data.user, opts: options },
            ])

            if (this.userData.is_branch_user) {
              await this.$axios
                .get(`clients/${this.userData.admin_id}/show`)
                .then((resp) => {
                  this.$store.commit('localStorage/SET_ADMIN_DATA',resp.data.data)
                })
            }

            this.TriggerNotify(this.notify.type, this.$t('admin.login_success'))
            if (res.data.data.user.data_completed) {
              this.$router.replace(this.localePath({ name: 'dashboard' }))
            } else {
              this.$router.replace(
                this.localePath({ name: 'dashboard-profile' })
              )
            }
          } else if (this.notify.state == 3) {
            this.TriggerNotify(this.notify.type, this.notify.message)
            this.$router.push(
              this.localePath({
                name: 'auth-verify',
                query: { email: this.form.email, type: 'verification' },
              })
            )
          } else {
            this.TriggerNotify(this.notify.type, this.notify.message)
          }
        })

      this.disabled = false
    },
    async handleSignIn() {
      try {
        const googleUser = await this.$gAuth.signIn()
        if (!googleUser) {
          return null
        }
        this.googleInfo.email = googleUser.getBasicProfile().getEmail()
        this.googleInfo.name = googleUser.getBasicProfile().getName()
        this.googleInfo.provider_id = googleUser.getBasicProfile().getId()
        const form_data = new FormData()
        form_data.append('name', this.googleInfo.name)
        form_data.append('email', this.googleInfo.email)
        form_data.append('provider_id', this.googleInfo.provider_id)
        form_data.append('provider', this.googleInfo.provider)

        await this.$axios
          .post('/client/auth/socialLogin', form_data)
          .then((res) => {
            this.$store.dispatch('localStorage/response_handler', res.data)
            if (this.notify.state == 0) {
              const accessToken = `Bearer ${res.data.data.token}`
              const options = {
                path: '/',
                maxAge: 60 * 60 * 24,
              }
              this.$store.commit(
                'localStorage/SET_USER_DATA',
                res.data.data.user
              )
              this.$store.commit('localStorage/SET_USER_TOKEN', accessToken)
              this.$cookies.setAll([
                { name: 'userToken', value: accessToken, opts: options },
                { name: 'userData', value: res.data.data.user, opts: options },
              ])
              this.TriggerNotify(this.notify.type, this.notify.message)
              if (res.data.data.user.data_completed) {
                this.$router.replace(this.localePath({ name: 'dashboard' }))
              } else {
                this.$router.replace(
                  this.localePath({ name: 'dashboard-profile' })
                )
              }
            } else if (this.notify.state == 3) {
              this.TriggerNotify(this.notify.type, this.notify.message)
              this.$router.push(
                this.localePath({
                  name: 'auth-verify',
                  query: { email: this.form.email, type: 'verification' },
                })
              )
            } else {
              this.TriggerNotify(this.notify.type, this.notify.message)
            }
          })

        if (this.notify.state === 0) {
        } else {
        }
      } catch (error) {
        console.log(error)
        return null
      }
    },
  },
}
